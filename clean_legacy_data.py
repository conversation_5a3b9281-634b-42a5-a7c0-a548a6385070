#!/usr/bin/env python3
"""
Legacy Data Cleaner for Scholars_XP
Cleans and formats legacy submission data for import
"""

import re
import csv
from datetime import datetime

def clean_discord_handle(handle):
    """Clean and normalize Discord handles"""
    if not handle or handle.strip() == '':
        return None
    
    # Remove @ prefix and extra spaces
    handle = handle.strip().lstrip('@').strip()
    
    # Remove parentheses and content inside them
    handle = re.sub(r'\s*\([^)]*\)\s*', '', handle)
    
    # Remove extra spaces and special characters
    handle = re.sub(r'[^\w\-_.]', '', handle)
    
    # Skip if empty after cleaning
    if not handle or len(handle) < 2:
        return None
        
    return handle

def extract_urls(url_text):
    """Extract valid URLs from text"""
    if not url_text:
        return []
    
    # Find all URLs in the text
    url_pattern = r'https?://[^\s\n"]+'
    urls = re.findall(url_pattern, url_text)
    
    # Clean URLs
    cleaned_urls = []
    for url in urls:
        # Remove trailing punctuation
        url = re.sub(r'[,;"\s]+$', '', url)
        if url and len(url) > 10:  # Basic validation
            cleaned_urls.append(url)
    
    return cleaned_urls

def extract_xp_from_notes(notes):
    """Extract XP value from notes if present"""
    if not notes:
        return None, notes
    
    # Look for numbers that might be XP values
    xp_patterns = [
        r'\b(\d{1,3})\s*(?:xp|points?|pts?)?\b',  # "30 XP" or "30"
        r'\b(\d{1,3})\s*$',  # Number at end
    ]
    
    for pattern in xp_patterns:
        match = re.search(pattern, notes, re.IGNORECASE)
        if match:
            xp_value = int(match.group(1))
            if 0 <= xp_value <= 200:  # Reasonable XP range
                # Remove XP from notes
                cleaned_notes = re.sub(pattern, '', notes, flags=re.IGNORECASE).strip()
                return xp_value, cleaned_notes if cleaned_notes else None
    
    return None, notes

def clean_legacy_data(input_file, output_file):
    """Clean the legacy data file"""
    
    cleaned_entries = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into lines but handle multi-line entries
    lines = content.split('\n')
    current_entry = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Check if this looks like a new entry (starts with date)
        if re.match(r'^\d{1,2}/\d{1,2}/\d{4}', line):
            # Process previous entry if exists
            if current_entry:
                process_entry(current_entry, cleaned_entries)
            current_entry = line
        else:
            # Continuation of previous entry
            current_entry += " " + line
    
    # Process last entry
    if current_entry:
        process_entry(current_entry, cleaned_entries)
    
    # Write cleaned data
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, delimiter='\t')
        writer.writerow(['Timestamp', 'Discord Handle', 'Role', 'URL', 'XP', 'Notes'])
        
        for entry in cleaned_entries:
            writer.writerow(entry)
    
    print(f"✅ Cleaned {len(cleaned_entries)} entries")
    print(f"📄 Output saved to: {output_file}")

def process_entry(entry_text, cleaned_entries):
    """Process a single entry"""
    # Split by tabs
    parts = entry_text.split('\t')
    
    if len(parts) < 4:
        print(f"⚠️  Skipping malformed entry: {entry_text[:50]}...")
        return
    
    timestamp = parts[0].strip()
    discord_handle = clean_discord_handle(parts[1])
    role = parts[2].strip()
    url_text = parts[3].strip()
    notes = parts[4].strip() if len(parts) > 4 else ""
    
    # Skip if no valid Discord handle
    if not discord_handle:
        print(f"⚠️  Skipping entry with invalid Discord handle: {parts[1]}")
        return
    
    # Extract URLs
    urls = extract_urls(url_text)
    if not urls:
        print(f"⚠️  Skipping entry with no valid URLs: {discord_handle}")
        return
    
    # Extract XP from notes
    xp_value, cleaned_notes = extract_xp_from_notes(notes)
    
    # Create separate entries for each URL
    for url in urls:
        cleaned_entries.append([
            timestamp,
            discord_handle,
            role,
            url,
            xp_value if xp_value else '',
            cleaned_notes if cleaned_notes else ''
        ])

if __name__ == "__main__":
    print("🧹 Cleaning legacy data...")
    clean_legacy_data("legacy.md", "legacy_cleaned.tsv")
    print("✨ Done! Import the cleaned file: legacy_cleaned.tsv")
